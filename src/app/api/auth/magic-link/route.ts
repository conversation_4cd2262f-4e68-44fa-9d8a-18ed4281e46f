import { NextRequest, NextResponse } from 'next/server'
import { verifyMagic<PERSON>inkToken, createSession } from '@/lib/magic-link-auth'
import { logMagicLinkSuccess, logMagicLinkFailure } from '@/lib/auth-logger'
import { getRequestContext } from '@/lib/logger'
import { withErrorHandling } from '@/lib/api-error-handler'

export const POST = withErrorHandling(async (request: NextRequest) => {
  const requestContext = getRequestContext(request)
  const body = await request.json()
  const { token } = body



  if (!token) {
    await logMagicLinkFailure(
      'unknown',
      'missing_token',
      'Token is required',
      undefined,
      requestContext.ip,
      requestContext.userAgent
    )
    return NextResponse.json(
      { error: 'Token is required' },
      { status: 400 }
    )
  }

  try {
    // Verify magic link token
    const user = await verifyMagicLinkToken(token)



    // Create session
    await createSession(user.id)

    // Log successful verification
    await logMagicLinkSuccess(
      user.email,
      token,
      requestContext.ip,
      requestContext.userAgent
    )

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
      }
    })

  } catch (error) {
    if (error instanceof Error) {
      if (error.message === 'Invalid or expired magic link') {
        await logMagicLinkFailure(
          token,
          'invalid_token',
          error.message,
          undefined,
          requestContext.ip,
          requestContext.userAgent
        )
        return NextResponse.json(
          { error: error.message },
          { status: 401 }
        )
      }

      if (error.message === 'Magic link has expired') {
        await logMagicLinkFailure(
          token,
          'expired_token',
          error.message,
          undefined,
          requestContext.ip,
          requestContext.userAgent
        )
        return NextResponse.json(
          { error: error.message },
          { status: 401 }
        )
      }

      if (error.message === 'User not found') {
        await logMagicLinkFailure(
          token,
          'user_not_found',
          error.message,
          undefined,
          requestContext.ip,
          requestContext.userAgent
        )
        return NextResponse.json(
          { error: 'User account not found' },
          { status: 404 }
        )
      }

      // Log other errors
      await logMagicLinkFailure(
        token,
        'unknown_error',
        error.message,
        undefined,
        requestContext.ip,
        requestContext.userAgent,
        { errorStack: error.stack }
      )
    }

    throw error
  }
})
