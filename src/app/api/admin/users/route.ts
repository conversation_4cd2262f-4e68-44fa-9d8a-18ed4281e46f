import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { requireAdmin, getCurrentUser } from '@/lib/auth'
import { logUserDeleted } from '@/lib/activity-logger'
import { withErrorHandling } from '@/lib/api-error-handler'

export const GET = withErrorHandling(async (request: NextRequest) => {
  await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search')
    const sort = searchParams.get('sort') || 'created_at' // 'email', 'created_at'
    
    const offset = (page - 1) * limit
    
    let whereClause = ''
    const params: (string | number)[] = []
    let paramIndex = 1
    
    if (search) {
      whereClause = `WHERE (u.email ILIKE $${paramIndex} OR u.first_name ILIKE $${paramIndex} OR u.last_name ILIKE $${paramIndex})`
      params.push(`%${search}%`)
      paramIndex++
    }
    
    // Get users with company information (using automatic email domain matching)
    const usersQuery = `
      SELECT
        u.*,
        -- Company assignment via users.company_id (automatic email domain matching)
        c.id as company_id,
        c.name as company_name,
        'domain' as company_assignment_type,

        COUNT(s.id) as active_sessions
      FROM users u
      -- Company assignment via users.company_id
      LEFT JOIN companies c ON u.company_id = c.id
      LEFT JOIN user_sessions s ON u.id = s.user_id AND s.expires_at > NOW()
      ${whereClause}
      GROUP BY u.id, u.email, u.first_name, u.last_name, u.role, u.payment_status, u.email_verified, u.created_at, u.updated_at,
               c.id, c.name
      ORDER BY ${sort === 'email' ? 'u.email ASC' : 'u.created_at DESC'}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `
    
    params.push(limit, offset)
    
    const usersResult = await query(usersQuery, params)
    
    // Get total count
    const countQuery = `
      SELECT COUNT(DISTINCT u.id) as total
      FROM users u
      ${whereClause}
    `
    
    const countResult = await query(countQuery, params.slice(0, -2))
    const total = parseInt(countResult.rows[0].total)
    
    return NextResponse.json({
      users: usersResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
})

// DELETE /api/admin/users - Delete a user
export const DELETE = withErrorHandling(async (request: NextRequest) => {
  await requireAdmin()

    // Get current admin user for activity logging
    const adminUser = await getCurrentUser()
    if (!adminUser) {
      return NextResponse.json(
        { error: 'Admin user not found' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Check if user exists and get full user details for logging
    const userResult = await query(
      'SELECT id, email, first_name, last_name FROM users WHERE id = $1',
      [userId]
    )

    if (userResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    const user = userResult.rows[0]

    // Check all related records before deletion
    const benefitVerificationsResult = await query(
      'SELECT COUNT(*) as count FROM benefit_verifications WHERE user_id = $1',
      [userId]
    )

    const savedCompaniesResult = await query(
      'SELECT COUNT(*) as count FROM saved_companies WHERE user_id = $1',
      [userId]
    )

    const userSessionsResult = await query(
      'SELECT COUNT(*) as count FROM user_sessions WHERE user_id = $1',
      [userId]
    )

    const companyVerificationTokensResult = await query(
      'SELECT COUNT(*) as count FROM company_verification_tokens WHERE user_id = $1',
      [userId]
    )

    // Delete related records manually
    await query('DELETE FROM benefit_verifications WHERE user_id = $1', [userId])
    await query('DELETE FROM company_verification_tokens WHERE user_id = $1', [userId])

    // saved_companies and user_sessions should cascade automatically

    // Delete user
    await query('DELETE FROM users WHERE id = $1', [userId])

    // Log the user deletion activity
    await logUserDeleted(
      user.id,
      user.email,
      adminUser.id,
      adminUser.email,
      user.first_name && user.last_name ? `${user.first_name} ${user.last_name}` : undefined,
      adminUser.firstName && adminUser.lastName ? `${adminUser.firstName} ${adminUser.lastName}` : undefined
    )

    return NextResponse.json({
      message: 'User deleted successfully',
      deletedUser: userResult.rows[0]
    })
})


